-webkit-locale: "en";
margin: 0;
padding: 0;
box-sizing: border-box;
-webkit-text-size-adjust: 100%;
font-size: var(--oj-html-font-size);
font-family: 'Oracle Sans',
-apple-system,
BlinkMacSystemFont,
"Segoe UI",
"Helvetica Neue",
Arial,
sans-serif;
font-variant-numeric: tabular-nums;
--oj-palette-danger-rgb-10: 255,
248,
247;
--oj-palette-danger-rgb-20: 255,
241,
239;
--oj-palette-danger-rgb-30: 255,
235,
232;
--oj-palette-danger-rgb-40: 255,
217,
211;
--oj-palette-danger-rgb-50: 255,
193,
184;
--oj-palette-danger-rgb-60: 255,
157,
144;
--oj-palette-danger-rgb-70: 255,
134,
117;
--oj-palette-danger-rgb-80: 254,
104,
84;
--oj-palette-danger-rgb-90: 236,
79,
58;
--oj-palette-danger-rgb-100: 214,
59,
37;
--oj-palette-danger-rgb-110: 195,
53,
34;
--oj-palette-danger-rgb-120: 179,
49,
31;
--oj-palette-danger-rgb-130: 170,
34,
34;
--oj-palette-danger-rgb-140: 143,
39,
25;
--oj-palette-danger-rgb-150: 124,
34,
22;
--oj-palette-danger-rgb-160: 102,
28,
18;
--oj-palette-danger-rgb-170: 86,
24,
15;
--oj-palette-warning-rgb-10: 254,
249,
242;
--oj-palette-warning-rgb-20: 253,
242,
229;
--oj-palette-warning-rgb-30: 252,
237,
220;
--oj-palette-warning-rgb-40: 249,
221,
188;
--oj-palette-warning-rgb-50: 246,
199,
146;
--oj-palette-warning-rgb-60: 240,
169,
87;
--oj-palette-warning-rgb-70: 235,
150,
50;
--oj-palette-warning-rgb-80: 225,
128,
18;
--oj-palette-warning-rgb-90: 198,
113,
14;
--oj-palette-warning-rgb-100: 172,
99,
12;
--oj-palette-warning-rgb-110: 156,
89,
11;
--oj-palette-warning-rgb-120: 143,
82,
10;
--oj-palette-warning-rgb-130: 129,
73,
9;
--oj-palette-warning-rgb-140: 114,
65,
8;
--oj-palette-warning-rgb-150: 99,
56,
7;
--oj-palette-warning-rgb-160: 81,
47,
6;
--oj-palette-warning-rgb-170: 69,
39,
5;
--oj-palette-success-rgb-10: 244,
252,
235;
--oj-palette-success-rgb-20: 235,
248,
222;
--oj-palette-success-rgb-30: 228,
245,
211;
--oj-palette-success-rgb-40: 207,
235,
179;
--oj-palette-success-rgb-50: 177,
221,
136;
--oj-palette-success-rgb-60: 138,
201,
79;
--oj-palette-success-rgb-70: 125,
186,
69;
--oj-palette-success-rgb-80: 111,
169,
57;
--oj-palette-success-rgb-90: 94,
148,
43;
--oj-palette-success-rgb-100: 80,
130,
35;
--oj-palette-success-rgb-110: 73,
118,
32;
--oj-palette-success-rgb-120: 67,
107,
29;
--oj-palette-success-rgb-130: 60,
96,
26;
--oj-palette-success-rgb-140: 53,
86,
23;
--oj-palette-success-rgb-150: 46,
73,
20;
--oj-palette-success-rgb-160: 38,
61,
16;
--oj-palette-success-rgb-170: 31,
51,
14;
--oj-palette-info-rgb-10: 246,
250,
252;
--oj-palette-info-rgb-20: 237,
246,
249;
--oj-palette-info-rgb-30: 228,
241,
247;
--oj-palette-info-rgb-40: 208,
229,
238;
--oj-palette-info-rgb-50: 180,
213,
225;
--oj-palette-info-rgb-60: 143,
191,
208;
--oj-palette-info-rgb-70: 121,
177,
198;
--oj-palette-info-rgb-80: 95,
162,
186;
--oj-palette-info-rgb-90: 65,
144,
172;
--oj-palette-info-rgb-100: 34,
126,
158;
--oj-palette-info-rgb-110: 14,
114,
151;
--oj-palette-info-rgb-120: 0,
104,
140;
--oj-palette-info-rgb-130: 2,
94,
126;
--oj-palette-info-rgb-140: 4,
83,
111;
--oj-palette-info-rgb-150: 6,
72,
95;
--oj-palette-info-rgb-160: 6,
60,
78;
--oj-palette-info-rgb-170: 5,
50,
66;
--oj-palette-brand-rgb-10: 247,
252,
243;
--oj-palette-brand-rgb-20: 237,
248,
230;
--oj-palette-brand-rgb-30: 229,
244,
220;
--oj-palette-brand-rgb-40: 208,
234,
193;
--oj-palette-brand-rgb-50: 189,
217,
174;
--oj-palette-brand-rgb-60: 163,
193,
148;
--oj-palette-brand-rgb-70: 148,
179,
130;
--oj-palette-brand-rgb-80: 130,
163,
114;
--oj-palette-brand-rgb-90: 111,
145,
93;
--oj-palette-brand-rgb-100: 95,
125,
79;
--oj-palette-brand-rgb-110: 87,
115,
70;
--oj-palette-brand-rgb-120: 79,
105,
63;
--oj-palette-brand-rgb-130: 70,
95,
56;
--oj-palette-brand-rgb-140: 61,
84,
49;
--oj-palette-brand-rgb-150: 53,
72,
42;
--oj-palette-brand-rgb-160: 43,
58,
33;
--oj-palette-brand-rgb-170: 37,
51,
29;
--oj-palette-neutral-rgb-0: 255,
255,
255;
--oj-palette-neutral-rgb-10: 251,
249,
248;
--oj-palette-neutral-rgb-20: 245,
244,
242;
--oj-palette-neutral-rgb-30: 241,
239,
237;
--oj-palette-neutral-rgb-40: 228,
225,
221;
--oj-palette-neutral-rgb-50: 212,
207,
202;
--oj-palette-neutral-rgb-60: 188,
182,
177;
--oj-palette-neutral-rgb-70: 174,
168,
162;
--oj-palette-neutral-rgb-80: 158,
152,
146;
--oj-palette-neutral-rgb-90: 139,
133,
128;
--oj-palette-neutral-rgb-100: 123,
117,
112;
--oj-palette-neutral-rgb-110: 111,
105,
100;
--oj-palette-neutral-rgb-120: 101,
95,
91;
--oj-palette-neutral-rgb-130: 92,
86,
81;
--oj-palette-neutral-rgb-140: 81,
76,
71;
--oj-palette-neutral-rgb-150: 71,
66,
62;
--oj-palette-neutral-rgb-160: 58,
54,
50;
--oj-palette-neutral-rgb-170: 49,
45,
42;
--oj-palette-neutral-rgb-180: 32,
30,
28;
--oj-palette-neutral-rgb-190: 22,
21,
19;
--oj-palette-neutral-rgb-200: 00,
00,
00;
--oj-palette-dvt-rgb-1: 36,
93,
99;
--oj-palette-dvt-rgb-2: 222,
127,
17;
--oj-palette-dvt-rgb-3: 95,
185,
181;
--oj-palette-dvt-rgb-4: 78,
65,
55;
--oj-palette-dvt-rgb-5: 160,
201,
139;
--oj-palette-dvt-rgb-6: 180,
114,
130;
--oj-palette-dvt-rgb-7: 131,
64,
30;
--oj-palette-dvt-rgb-8: 158,
127,
204;
--oj-palette-dvt-rgb-9: 251,
194,
106;
--oj-palette-dvt-rgb-10: 88,
49,
110;
--oj-palette-dvt-rgb-11: 95,
162,
186;
--oj-palette-dvt-rgb-12: 49,
122,
69;
--oj-core-text-color-primary: rgb(var(--oj-palette-neutral-rgb-190));
--oj-core-text-color-secondary: rgba(var(--oj-palette-neutral-rgb-190), .65);
--oj-core-text-color-disabled: rgba(var(--oj-palette-neutral-rgb-190), .4);
--oj-core-text-color-brand: rgb(var(--oj-palette-brand-rgb-110));
--oj-core-text-color-danger: rgb(var(--oj-palette-danger-rgb-120));
--oj-core-text-color-warning: rgb(var(--oj-palette-warning-rgb-120));
--oj-core-text-color-success: rgb(var(--oj-palette-success-rgb-120));
--oj-core-bg-color-content: rgb(var(--oj-palette-neutral-rgb-0));
--oj-core-bg-color-hover: rgba(var(--oj-palette-neutral-rgb-190), .04);
--oj-core-bg-color-active: rgba(var(--oj-palette-neutral-rgb-190), .06);
--oj-core-bg-color-selected: rgb(var(--oj-palette-brand-rgb-30));
--oj-core-border-color-selected: rgb(var(--oj-palette-brand-rgb-110));
--oj-core-color-disabled-1: rgba(var(--oj-palette-neutral-rgb-190), 0.05);
--oj-core-color-disabled-2: rgba(var(--oj-palette-neutral-rgb-190), .1);
--oj-core-icon-size-lg: 1.5rem;
--oj-core-icon-size-sm: 1rem;
--oj-core-divider-color: rgba(var(--oj-palette-neutral-rgb-190), .1);
--oj-core-divider-margin: 0.5rem;
--oj-core-neutral-1: rgb(var(--oj-palette-neutral-rgb-100));
--oj-core-neutral-2: rgb(var(--oj-palette-neutral-rgb-110));
--oj-core-neutral-3: rgb(var(--oj-palette-neutral-rgb-120));
--oj-core-neutral-contrast: rgb(var(--oj-palette-neutral-rgb-0));
--oj-core-neutral-secondary-1: rgb(var(--oj-palette-neutral-rgb-30));
--oj-core-neutral-secondary-2: rgb(var(--oj-palette-neutral-rgb-20));
--oj-core-neutral-secondary-3: rgb(var(--oj-palette-neutral-rgb-10));
--oj-core-neutral-secondary-contrast: rgb(var(--oj-palette-neutral-rgb-120));
--oj-core-brand-1: rgb(var(--oj-palette-brand-rgb-100));
--oj-core-brand-2: rgb(var(--oj-palette-brand-rgb-110));
--oj-core-brand-3: rgb(var(--oj-palette-brand-rgb-120));
--oj-core-brand-contrast: rgb(var(--oj-palette-neutral-rgb-0));
--oj-core-danger-1: rgb(var(--oj-palette-danger-rgb-100));
--oj-core-danger-2: rgb(var(--oj-palette-danger-rgb-110));
--oj-core-danger-3: rgb(var(--oj-palette-danger-rgb-120));
--oj-core-danger-contrast: rgb(var(--oj-palette-neutral-rgb-0));
--oj-core-danger-secondary-1: rgb(var(--oj-palette-danger-rgb-30));
--oj-core-danger-secondary-2: rgb(var(--oj-palette-danger-rgb-20));
--oj-core-danger-secondary-3: rgb(var(--oj-palette-danger-rgb-10));
--oj-core-danger-secondary-contrast: rgb(var(--oj-palette-danger-rgb-120));
--oj-core-warning-1: rgb(var(--oj-palette-warning-rgb-100));
--oj-core-warning-2: rgb(var(--oj-palette-warning-rgb-110));
--oj-core-warning-3: rgb(var(--oj-palette-warning-rgb-120));
--oj-core-warning-contrast: rgb(var(--oj-palette-neutral-rgb-0));
--oj-core-warning-secondary-1: rgb(var(--oj-palette-warning-rgb-30));
--oj-core-warning-secondary-2: rgb(var(--oj-palette-warning-rgb-20));
--oj-core-warning-secondary-3: rgb(var(--oj-palette-warning-rgb-10));
--oj-core-warning-secondary-contrast: rgb(var(--oj-palette-warning-rgb-120));
--oj-core-success-1: rgb(var(--oj-palette-success-rgb-100));
--oj-core-success-2: rgb(var(--oj-palette-success-rgb-110));
--oj-core-success-3: rgb(var(--oj-palette-success-rgb-120));
--oj-core-success-contrast: rgb(var(--oj-palette-neutral-rgb-0));
--oj-core-success-secondary-1: rgb(var(--oj-palette-success-rgb-30));
--oj-core-success-secondary-2: rgb(var(--oj-palette-success-rgb-20));
--oj-core-success-secondary-3: rgb(var(--oj-palette-success-rgb-10));
--oj-core-success-secondary-contrast: rgb(var(--oj-palette-success-rgb-120));
--oj-core-info-1: rgb(var(--oj-palette-info-rgb-100));
--oj-core-info-2: rgb(var(--oj-palette-info-rgb-110));
--oj-core-info-3: rgb(var(--oj-palette-info-rgb-120));
--oj-core-info-contrast: rgb(var(--oj-palette-neutral-rgb-0));
--oj-core-info-secondary-1: rgb(var(--oj-palette-info-rgb-30));
--oj-core-info-secondary-2: rgb(var(--oj-palette-info-rgb-20));
--oj-core-info-secondary-3: rgb(var(--oj-palette-info-rgb-10));
--oj-core-info-secondary-contrast: rgb(var(--oj-palette-info-rgb-120));
--oj-core-focus-border-color: rgb(var(--oj-palette-neutral-rgb-190));
--oj-core-cursor-clickable: pointer;
--oj-core-drag-drop-color-1: rgb(var(--oj-palette-brand-rgb-40));
--oj-core-drag-drop-color-2: rgb(var(--oj-palette-brand-rgb-100));
--oj-core-drag-drop-line-color: rgb(var(--oj-palette-brand-rgb-100));
--oj-core-touch-target-min-size: 2.25rem;
--oj-core-box-shadow-rgb: var(--oj-palette-neutral-rgb-200);
--oj-core-box-shadow-xs: 0px 1px 4px 0px rgba(var(--oj-core-box-shadow-rgb), .12);
--oj-core-box-shadow-sm: 0px 4px 8px 0px rgba(var(--oj-core-box-shadow-rgb), .16);
--oj-core-box-shadow-md: 0px 6px 12px 0px rgba(var(--oj-core-box-shadow-rgb), .2);
--oj-core-box-shadow-lg: 0px 8px 16px 0px rgba(var(--oj-core-box-shadow-rgb), 0.24);
--oj-core-box-shadow-xl: 0px 12px 20px 0px rgba(var(--oj-core-box-shadow-rgb), 0.28);
--oj-core-dropdown-box-shadow: var(--oj-core-box-shadow-sm);
--oj-private-core-global-dropdown-offset: 4;
--oj-core-scrim-color: rgba(var(--oj-palette-neutral-rgb-190), .4);
--oj-core-spacing-1x: .25rem;
--oj-core-spacing-2x: .5rem;
--oj-core-spacing-3x: .75rem;
--oj-core-spacing-4x: 1rem;
--oj-core-spacing-5x: 1.25rem;
--oj-core-spacing-6x: 1.5rem;
--oj-core-spacing-7x: 1.75rem;
--oj-core-spacing-8x: 2rem;
--oj-core-spacing-9x: 2.25rem;
--oj-core-spacing-10x: 2.5rem;
--oj-core-spacing-11x: 2.75rem;
--oj-core-spacing-12x: 3rem;
--oj-private-core-wrappable-margin-bottom: 0px;
--oj-core-border-radius-sm: 2px;
--oj-core-border-radius-md: .25rem;
--oj-core-border-radius-lg: .375rem;
--oj-core-border-radius-xl: .5rem;
--oj-private-core-z-index-fixed: 100;
--oj-private-core-z-index-off-canvas: 200;
--oj-private-core-z-index-resizable: 900;
--oj-core-z-index-popup: 1000;
--oj-core-z-index-dialog: 1050;
--oj-core-z-index-messages: 2000;
--oj-private-core-global-loading-indicator-delay-duration: 0.05s;
--oj-animation-duration-xshort: 0.1s;
--oj-animation-duration-short: 0.15s;
--oj-animation-duration-medium: 0.2s;
--oj-animation-duration-long: 0.3s;
--oj-animation-duration-xlong: 0.5s;
--oj-animation-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
--oj-animation-ease-out: cubic-bezier(0, 0, 0.2, 1);
--oj-animation-ease-in: cubic-bezier(0.4, 0, 1, 1);
--oj-animation-effect-ripple-bg-color: rgb(var(--oj-palette-neutral-rgb-160));
--oj-animation-skeleton-bg-start-color: rgba(var(--oj-palette-neutral-rgb-170), 0.05);
--oj-animation-skeleton-bg-end-color: rgba(var(--oj-palette-neutral-rgb-170), 0.15);
--oj-private-animation-global-card-entrance-delay-increment: 50;
--oj-html-font-family: -apple-system,
BlinkMacSystemFont,
"Segoe UI",
"Helvetica Neue",
Arial,
sans-serif;
--oj-html-font-size: 1em;
--oj-body-bg-color: rgb(var(--oj-palette-neutral-rgb-10));
--oj-heading-text-color: var(--oj-core-text-color-primary);
--oj-heading-margin-bottom: 1rem;
--oj-heading-margin-top: 1rem;
--oj-subheading-margin-top: 0;
--oj-subheading-margin-bottom: 0.75rem;
--oj-paragraph-margin-bottom: 1rem;
--oj-list-margin-top: 0.5rem;
--oj-list-nested-padding-start: 2.5rem;
--oj-list-item-line-height: 1.5;
--oj-list-unordered-style-type: square;
--oj-link-text-decoration: none;
--oj-link-text-decoration-hover: underline;
--oj-link-text-color: rgb(var(--oj-palette-info-rgb-120));
--oj-link-text-color-active: var(--oj-link-text-color);
--oj-link-text-color-visited: var(--oj-link-text-color);
--oj-link-embedded-text-decoration: none;
--oj-link-embedded-text-decoration-hover: none;
--oj-link-embedded-border-bottom-width: 1px;
--oj-link-embedded-border-bottom-width-hover: 0px;
--oj-typography-heading-2xl-font-size: 2.5rem;
--oj-typography-heading-2xl-font-weight: 900;
--oj-typography-heading-2xl-line-height: 1.3;
--oj-typography-heading-xl-font-size: 2.25rem;
--oj-typography-heading-xl-font-weight: 900;
--oj-typography-heading-xl-line-height: 1.222;
--oj-typography-heading-lg-font-size: 2rem;
--oj-typography-heading-lg-font-weight: 900;
--oj-typography-heading-lg-line-height: 1.25;
--oj-typography-heading-md-font-size: 1.75rem;
--oj-typography-heading-md-font-weight: 900;
--oj-typography-heading-md-line-height: 1.2857;
--oj-typography-heading-sm-font-size: 1.5rem;
--oj-typography-heading-sm-font-weight: 900;
--oj-typography-heading-sm-line-height: 1.3333;
--oj-typography-heading-xs-font-size: 1.25rem;
--oj-typography-heading-xs-font-weight: 900;
--oj-typography-heading-xs-line-height: 1.4;
--oj-typography-subheading-2xl-font-size: 2.25rem;
--oj-typography-subheading-2xl-font-weight: bold;
--oj-typography-subheading-2xl-line-height: 1.2222;
--oj-typography-subheading-xl-font-size: 2rem;
--oj-typography-subheading-xl-font-weight: bold;
--oj-typography-subheading-xl-line-height: 1.25;
--oj-typography-subheading-lg-font-size: 1.75rem;
--oj-typography-subheading-lg-font-weight: bold;
--oj-typography-subheading-lg-line-height: 1.2857;
--oj-typography-subheading-md-font-size: 1.5rem;
--oj-typography-subheading-md-font-weight: bold;
--oj-typography-subheading-md-line-height: 1.3333;
--oj-typography-subheading-sm-font-size: 1.25rem;
--oj-typography-subheading-sm-font-weight: bold;
--oj-typography-subheading-sm-line-height: 1.4;
--oj-typography-subheading-xs-font-size: 1rem;
--oj-typography-subheading-xs-font-weight: bold;
--oj-typography-subheading-xs-line-height: 1.5;
--oj-typography-body-xl-font-size: 1.25rem;
--oj-typography-body-xl-line-height: 1.4;
--oj-typography-body-lg-font-size: 1.125rem;
--oj-typography-body-lg-line-height: 1.3333;
--oj-typography-body-md-font-size: 1rem;
--oj-typography-body-md-line-height: 1.25;
--oj-typography-body-sm-font-size: 0.859rem;
--oj-typography-body-sm-line-height: 1.2;
--oj-typography-body-xs-font-size: 0.75rem;
--oj-typography-body-xs-line-height: 1.3333;
--oj-typography-body-2xs-font-size: 0.625rem;
--oj-typography-body-2xs-line-height: 1.2;
--oj-scrollbar-thumb-color-hover: rgba(var(--oj-palette-neutral-rgb-170), .15);
--oj-scrollbar-track-color-force: rgba(var(--oj-palette-neutral-rgb-170), .05);
--oj-button-border-radius: var(--oj-core-border-radius-md);
--oj-button-font-weight: 600;
--oj-button-height: 2.75rem;
--oj-button-sm-height: 2.25rem;
--oj-button-lg-height: 3.25rem;
--oj-button-font-size: var(--oj-typography-body-sm-font-size);
--oj-button-sm-font-size: var(--oj-typography-body-sm-font-size);
--oj-button-lg-font-size: var(--oj-typography-body-sm-font-size);
--oj-button-text-to-edge-padding: 1rem;
--oj-button-sm-text-to-edge-padding: 1rem;
--oj-button-lg-text-to-edge-padding: 1.5rem;
--oj-button-icon-to-text-padding: .625rem;
--oj-button-sm-icon-to-text-padding: .5rem;
--oj-button-lg-icon-to-text-padding: 1rem;
--oj-button-icon-to-edge-padding: .625rem;
--oj-button-sm-icon-to-edge-padding: .5rem;
--oj-button-lg-icon-to-edge-padding: 1rem;
--oj-button-solid-chrome-bg-color: rgba(var(--oj-palette-neutral-rgb-190), .08);
--oj-button-solid-chrome-border-color: transparent;
--oj-button-solid-chrome-text-color: var(--oj-core-text-color-primary);
--oj-button-solid-chrome-bg-color-hover: rgba(var(--oj-palette-neutral-rgb-190), .12);
--oj-button-solid-chrome-border-color-hover: transparent;
--oj-button-solid-chrome-text-color-hover: var(--oj-core-text-color-primary);
--oj-button-solid-chrome-bg-color-active: rgba(var(--oj-palette-neutral-rgb-190), .16);
--oj-button-solid-chrome-border-color-active: transparent;
--oj-button-solid-chrome-text-color-active: var(--oj-core-text-color-primary);
--oj-button-solid-chrome-bg-color-disabled: var(--oj-core-color-disabled-1);
--oj-button-solid-chrome-border-color-disabled: transparent;
--oj-button-solid-chrome-text-color-disabled: var(--oj-core-text-color-disabled);
--oj-button-solid-chrome-bg-color-selected: var(--oj-button-solid-chrome-bg-color);
--oj-button-solid-chrome-border-color-selected: var(--oj-button-solid-chrome-border-color);
--oj-button-solid-chrome-text-color-selected: var(--oj-button-solid-chrome-text-color);
--oj-button-solid-chrome-bg-color-selected-hover: var(--oj-button-solid-chrome-bg-color-active);
--oj-button-solid-chrome-border-color-selected-hover: var(--oj-button-solid-chrome-border-color-active);
--oj-button-solid-chrome-text-color-selected-hover: var(--oj-button-solid-chrome-text-color-active);
--oj-button-solid-chrome-bg-color-selected-disabled: rgba(var(--oj-palette-neutral-rgb-100), .3);
--oj-button-solid-chrome-border-color-selected-disabled: transparent;
--oj-button-solid-chrome-text-color-selected-disabled: var(--oj-core-text-color-disabled);
--oj-button-borderless-chrome-text-color: var(--oj-core-text-color-primary);
--oj-button-borderless-chrome-bg-color-hover: var(--oj-core-bg-color-hover);
--oj-button-borderless-chrome-border-color-hover: transparent;
--oj-button-borderless-chrome-text-color-hover: var(--oj-core-text-color-primary);
--oj-button-borderless-chrome-bg-color-active: var(--oj-core-bg-color-active);
--oj-button-borderless-chrome-border-color-active: transparent;
--oj-button-borderless-chrome-text-color-active: var(--oj-core-text-color-primary);
--oj-button-borderless-chrome-text-color-disabled: var(--oj-core-text-color-disabled);
--oj-button-borderless-chrome-bg-color-selected: transparent;
--oj-button-borderless-chrome-border-color-selected: rgba(var(--oj-palette-neutral-rgb-190), .5);
--oj-button-borderless-chrome-text-color-selected: var(--oj-core-text-color-primary);
--oj-button-borderless-chrome-bg-color-selected-hover: var(--oj-core-bg-color-hover);
--oj-button-borderless-chrome-border-color-selected-hover: rgba(var(--oj-palette-neutral-rgb-190), .5);
--oj-button-borderless-chrome-text-color-selected-hover: var(--oj-core-text-color-primary);
--oj-button-borderless-chrome-bg-color-selected-disabled: transparent;
--oj-button-borderless-chrome-border-color-selected-disabled: var(--oj-core-color-disabled-2);
--oj-button-borderless-chrome-text-color-selected-disabled: var(--oj-core-text-color-disabled);
--oj-button-outlined-chrome-border-color: rgba(var(--oj-palette-neutral-rgb-190), .5);
--oj-button-outlined-chrome-text-color: var(--oj-core-text-color-primary);
--oj-button-outlined-chrome-bg-color: transparent;
--oj-button-outlined-chrome-bg-color-hover: var(--oj-core-bg-color-hover);
--oj-button-outlined-chrome-border-color-hover: rgba(var(--oj-palette-neutral-rgb-190), .5);
--oj-button-outlined-chrome-text-color-hover: var(--oj-core-text-color-primary);
--oj-button-outlined-chrome-bg-color-active: var(--oj-core-bg-color-active);
--oj-button-outlined-chrome-border-color-active: rgba(var(--oj-palette-neutral-rgb-190), .5);
--oj-button-outlined-chrome-text-color-active: var(--oj-core-text-color-primary);
--oj-button-outlined-chrome-border-color-disabled: var(--oj-core-text-color-disabled);
--oj-button-outlined-chrome-text-color-disabled: var(--oj-core-text-color-disabled);
--oj-button-outlined-chrome-bg-color-disabled: transparent;
--oj-button-outlined-chrome-bg-color-selected: transparent;
--oj-button-outlined-chrome-border-color-selected: rgba(var(--oj-palette-neutral-rgb-190), .5);
--oj-button-outlined-chrome-text-color-selected: var(--oj-core-text-color-primary);
--oj-button-outlined-chrome-bg-color-selected-hover: var(--oj-core-bg-color-hover);
--oj-button-outlined-chrome-border-color-selected-hover: rgba(var(--oj-palette-neutral-rgb-190), .5);
--oj-button-outlined-chrome-text-color-selected-hover: var(--oj-core-text-color-primary);
--oj-button-outlined-chrome-bg-color-selected-disabled: transparent;
--oj-button-outlined-chrome-border-color-selected-disabled: var(--oj-button-outlined-chrome-border-color-disabled);
--oj-button-outlined-chrome-text-color-selected-disabled: var(--oj-core-text-color-disabled);
--oj-button-call-to-action-chrome-bg-color: rgb(var(--oj-palette-neutral-rgb-170));
--oj-button-call-to-action-chrome-border-color: transparent;
--oj-button-call-to-action-chrome-text-color: var(--oj-core-neutral-contrast);
--oj-button-call-to-action-chrome-bg-color-hover: rgb(var(--oj-palette-neutral-rgb-160));
--oj-button-call-to-action-chrome-border-color-hover: transparent;
--oj-button-call-to-action-chrome-text-color-hover: var(--oj-core-neutral-contrast);
--oj-button-call-to-action-chrome-bg-color-active: rgb(var(--oj-palette-neutral-rgb-150));
--oj-button-call-to-action-chrome-border-color-active: transparent;
--oj-button-call-to-action-chrome-text-color-active: var(--oj-core-neutral-contrast);
--oj-button-icon-size: var(--oj-core-icon-size-lg);
--oj-button-sm-icon-size: 1.25rem;
--oj-button-lg-icon-size: var(--oj-core-icon-size-lg);
--oj-private-button-global-chroming-default: outlined;
--oj-private-icon-color: var(--oj-core-text-color-primary);
--oj-private-icon-color-default: var(--oj-button-borderless-chrome-text-color);
--oj-private-icon-color-hover: var(--oj-button-borderless-chrome-text-color-hover);
--oj-private-icon-bg-color-hover: var(--oj-button-borderless-chrome-bg-color-hover);
--oj-private-icon-border-color-hover: var(--oj-button-borderless-chrome-border-color-hover);
--oj-private-icon-color-active: var(--oj-button-borderless-chrome-text-color-active);
--oj-private-icon-bg-color-active: var(--oj-button-borderless-chrome-bg-color-active);
--oj-private-icon-border-color-active: var(--oj-button-borderless-chrome-border-color-active);
--oj-private-icon-color-disabled: var(--oj-button-borderless-chrome-text-color-disabled);
--oj-private-app-layout-max-width: 1440px;
--oj-private-app-layout-offcanvas-width: 90%;
--oj-private-app-layout-offcanvas-max-width: 320px;
--oj-private-app-layout-hybrid-offcanvas-bg-color: rgb(var(--oj-palette-neutral-rgb-160));
--oj-private-app-layout-hybrid-padding: 1rem;
--oj-private-app-layout-hybrid-header-min-height: 56px;
--oj-private-app-layout-hybrid-header-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-private-app-layout-hybrid-header-border-color: var(--oj-core-divider-color);
--oj-private-app-layout-hybrid-header-border-bottom-width: 1px;
--oj-private-app-layout-hybrid-header-title-text-color: var(--oj-core-text-color-primary);
--oj-private-app-layout-hybrid-header-title-font-weight: 500;
--oj-private-app-layout-hybrid-header-title-font-size: var(--oj-typography-body-xl-font-size);
--oj-private-app-layout-hybrid-header-box-shadow: none;
--oj-private-app-layout-hybrid-footer-min-height: 48px;
--oj-private-app-layout-hybrid-footer-bg-color: var(--oj-private-app-layout-hybrid-header-bg-color);
--oj-private-app-layout-hybrid-footer-border-color: var(--oj-private-app-layout-hybrid-header-border-color);
--oj-private-app-layout-hybrid-footer-border-top-width: 1px;
--oj-private-app-layout-hybrid-nav-bar-box-shadow: none;
--oj-private-app-layout-web-offcanvas-bg-color: rgb(var(--oj-palette-neutral-rgb-160));
--oj-private-app-layout-web-padding: 20px;
--oj-private-app-layout-web-header-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-private-app-layout-web-header-border-color: var(--oj-core-divider-color);
--oj-private-app-layout-web-header-box-shadow: none;
--oj-private-app-layout-web-header-min-height: 3.143rem;
--oj-private-app-layout-web-header-title-text-color: var(--oj-core-text-color-secondary);
--oj-private-app-layout-web-header-title-font-size: var(--oj-typography-body-xl-font-size);
--oj-private-app-layout-web-header-title-font-weight: 600;
--oj-private-app-layout-web-footer-min-height: 3.571rem;
--oj-private-app-layout-web-footer-bg-color: var(--oj-private-app-layout-web-header-bg-color);
--oj-private-app-layout-web-footer-border-color: var(--oj-private-app-layout-web-header-border-color);
--oj-collection-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-collection-border-color: rgba(var(--oj-palette-neutral-rgb-190), .1);
--oj-collection-free-space-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-collection-list-row-height: 3rem;
--oj-collection-list-cell-padding-vertical: 0.75rem;
--oj-collection-grid-row-height: 2.375rem;
--oj-collection-header-font-size: var(--oj-typography-body-xs-font-size);
--oj-collection-header-font-weight: normal;
--oj-collection-header-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-collection-header-text-color: var(--oj-core-text-color-secondary);
--oj-collection-grid-header-height: 2.25rem;
--oj-collection-header-bg-color-hover: var(--oj-core-bg-color-hover);
--oj-collection-header-bg-color-selected: var(--oj-core-bg-color-selected);
--oj-collection-cell-banded-bg-color: rgb(var(--oj-palette-neutral-rgb-10));
--oj-collection-grid-cell-padding: .5rem;
--oj-collection-editable-cell-border-color-focus: rgb(var(--oj-palette-brand-rgb-110));
--oj-collection-editable-cell-bg-color-read-only: var(--oj-core-bg-color-hover);
--oj-collection-default-sort-icon-display: block;
--oj-list-view-group-header-bg-color: transparent;
--oj-list-view-group-header-bg-color-sticky: rgb(var(--oj-palette-neutral-rgb-30));
--oj-list-view-group-header-font-weight: var(--oj-typography-subheading-xs-font-weight);
--oj-list-view-group-header-font-size: var(--oj-typography-subheading-xs-font-size);
--oj-list-view-group-header-line-height: var(--oj-typography-subheading-xs-line-height);
--oj-list-view-item-padding-horizontal: 1rem;
--oj-private-list-view-global-load-indicator-default: skeleton;
--oj-private-list-view-global-add-animation-default: '[{"effect":"expand"},"fadeIn"]';
--oj-private-list-view-global-remove-animation-default: '[{"effect":"collapse"},"fadeOut"]';
--oj-private-list-view-global-update-animation-default: '{"effect": "fadeIn"}';
--oj-private-list-view-global-expand-animation-default: '{"effect": "expand"}';
--oj-private-list-view-global-collapse-animation-default: '{"effect": "collapse"}';
--oj-private-list-view-global-pointerUp-animation-default: '{"effect": "ripple"}';
--oj-private-list-view-global-card-entrance-animation-default: '[{"effect":"slideIn","offsetY":"300px","duration":"300ms"}, {"effect":"fadeIn","duration":"300ms"}] ';
--oj-private-list-view-global-card-exit-animation-default: '[{"effect":"slideOut","offsetY":"300px","duration":"300ms","persist":"all"},{"effect":"fadeOut","duration":"300ms","persist":"all"}]';
--oj-private-list-view-global-gridlines-item-default: hidden;
--oj-private-list-view-global-gridlines-top-default: hidden;
--oj-private-list-view-global-gridlines-bottom-default: hidden;
--oj-panel-padding: var(--oj-core-spacing-2x);
--oj-panel-border-radius: var(--oj-core-border-radius-lg);
--oj-panel-gutter: var(--oj-core-spacing-4x);
--oj-panel-bg-color: var(--oj-core-bg-color-content);
--oj-panel-border-color: var(--oj-core-divider-color);
--oj-action-card-scale-hover: 1;
--oj-avatar-bg-color: rgb(var(--oj-palette-neutral-rgb-130));
--oj-avatar-text-color: var(--oj-core-neutral-contrast);
--oj-avatar-pattern-display: block;
--oj-avatar-size: 4.5rem;
--oj-avatar-border-radius: var(--oj-core-border-radius-md);
--oj-avatar-initials-font-size: var(--oj-typography-heading-lg-font-size);
--oj-avatar-initials-font-weight: lighter;
--oj-avatar-icon-font-size: 2rem;
--oj-private-avatar-global-shape-default: square;
--oj-badge-bg-color: var(--oj-core-neutral-1);
--oj-badge-text-color: var(--oj-core-neutral-contrast);
--oj-badge-font-size: 0.6875rem;
--oj-badge-font-weight: 700;
--oj-badge-font-stretch: condensed;
--oj-badge-height: 1.5rem;
--oj-badge-border-radius: var(--oj-core-border-radius-lg);
--oj-private-buttonset-solid-chrome-internal-border-color: rgb(var(--oj-palette-neutral-rgb-100));
--oj-private-buttonset-solid-chrome-internal-border-color-active: rgb(var(--oj-palette-neutral-rgb-100));
--oj-private-buttonset-solid-chrome-internal-border-color-selected: rgb(var(--oj-palette-neutral-rgb-100));
--oj-private-buttonset-solid-chrome-internal-border-color-selected-disabled: var(--oj-button-solid-chrome-bg-color-disabled);
--oj-buttonset-outlined-chrome-internal-border-color: var(--oj-button-outlined-chrome-border-color);
--oj-buttonset-outlined-chrome-internal-border-color-active: var(--oj-button-outlined-chrome-border-color);
--oj-buttonset-outlined-chrome-internal-border-color-selected: var(--oj-button-outlined-chrome-border-color);
--oj-buttonset-outlined-chrome-internal-border-color-selected-disabled: var(--oj-core-color-disabled-1);
--oj-private-buttonset-global-chroming-default: outlined;
--oj-popup-border-color: var(--oj-core-divider-color);
--oj-popup-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-popup-border-radius: var(--oj-core-border-radius-sm);
--oj-popup-box-shadow: var(--oj-core-box-shadow-xs);
--oj-popup-padding: 0.5rem;
--oj-popup-tail-height: 5px;
--oj-popup-tail-width: 10px;
--oj-private-popup-global-modality-default: "modeless";
--oj-private-popup-global-open-animation-default: '[{"effect":"zoomIn","transformOrigin":"#myPosition"},"fadeIn"]';
--oj-private-popup-global-close-animation-default: '[{"effect":"zoomOut","transformOrigin":"#myPosition"},"fadeOut"]';
--oj-tooltip-text-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-tooltip-bg-color: rgb(var(--oj-palette-neutral-rgb-170));
--oj-tooltip-font-size: var(--oj-typography-body-xs-font-size);
--oj-tooltip-line-height: var(--oj-typography-body-xs-line-height);
--oj-tooltip-padding: var(--oj-core-spacing-2x);
--oj-tooltip-border-radius: var(--oj-core-border-radius-md);
--oj-tooltip-border-color: rgb(var(--oj-palette-neutral-rgb-170));
--oj-dvt-danger-color: rgb(214, 59, 37);
--oj-dvt-warning-color: rgb(207, 124, 0);
--oj-dvt-success-color: rgb(80, 130, 35);
--oj-dvt-item-border-color-selected: rgb(var(--oj-palette-neutral-rgb-190));
--oj-dvt-item-contrast-color: rgb(var(--oj-palette-neutral-rgb-20));
--oj-dvt-marquee-color: rgba(var(--oj-palette-neutral-rgb-20), .1);
--oj-dvt-marquee-border-color: rgba(var(--oj-palette-neutral-rgb-190), 0.8);
--oj-dvt-contrast-line-color: rgba(var(--oj-palette-neutral-rgb-20), 0.7);
--oj-dvt-reference-object-area-color: rgba(var(--oj-palette-neutral-rgb-170), 0.08);
--oj-dvt-reference-object-line-color: rgba(var(--oj-palette-neutral-rgb-190), 0.8);
--oj-dvt-overview-bg-color: rgb(var(--oj-palette-neutral-rgb-50));
--oj-dvt-overview-window-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-dvt-overview-window-border-color: rgb(var(--oj-palette-neutral-rgb-170));
--oj-dvt-label-font-size-sm: var(--oj-typography-body-2xs-font-size);
--oj-dvt-label-font-size-md: var(--oj-typography-body-xs-font-size);
--oj-dvt-node-border-color-hover: rgb(var(--oj-palette-neutral-rgb-20));
--oj-chart-axis-title-text-color: var(--oj-core-text-color-secondary);
--oj-chart-axis-tick-label-text-color: var(--oj-core-text-color-secondary);
--oj-chart-stock-falling-bg-color: var(--oj-dvt-danger-color);
--oj-chart-stock-range-bg-color: rgb(var(--oj-palette-neutral-rgb-100));
--oj-chart-stock-rising-bg-color: var(--oj-dvt-success-color);
--oj-chart-animation-rising-icon-color: rgb(var(--oj-palette-info-rgb-90));
--oj-chart-animation-falling-icon-color: rgb(var(--oj-palette-danger-rgb-90));
--oj-chart-animation-marker-color: rgb(var(--oj-palette-neutral-rgb-20));
--oj-chart-polar-axis-tick-label-outside-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-chart-polar-axis-tick-label-inside-bg-color: rgba(var(--oj-palette-neutral-rgb-0), .6);
--oj-chart-data-cursor-line-color: rgba(var(--oj-palette-neutral-rgb-190), 0.8);
--oj-label-font-size: var(--oj-typography-body-sm-font-size);
--oj-label-font-weight: 600;
--oj-label-line-height: var(--oj-typography-body-sm-line-height);
--oj-label-color: var(--oj-core-text-color-primary);
--oj-label-color-disabled: var(--oj-core-text-color-disabled);
--oj-label-inside-edge-font-size: var(--oj-typography-body-xs-font-size);
--oj-label-inside-edge-font-weight: 600;
--oj-label-inside-edge-line-height: var(--oj-typography-body-xs-line-height);
--oj-label-inside-edge-position-top: 0.5rem;
--oj-label-inside-edge-color: var(--oj-core-text-color-secondary);
--oj-label-required-icon-color: rgb(var(--oj-palette-brand-rgb-110));
--oj-label-required-char-font-size: 0.9rem;
--oj-form-layout-divider-width: 0;
--oj-form-layout-divider-margin: 0;
--oj-form-layout-start-edge-column-min-width: 22rem;
--oj-form-layout-start-edge-column-max-width: 37.5rem;
--oj-form-layout-column-min-width: 18rem;
--oj-form-layout-column-max-width: 28rem;
--oj-form-layout-horizontal-margin: 0px;
--oj-form-layout-column-gutter: 1.5rem;
--oj-form-layout-margin-bottom: .875rem;
--oj-form-layout-start-edge-label-text-align: end;
--oj-form-layout-start-edge-value-text-align: start;
--oj-form-layout-start-edge-label-to-value-padding: 1.5rem;
--oj-form-layout-top-edge-label-to-value-padding: 0.5rem;
--oj-form-control-font-weight-read-only: normal;
--oj-text-field-sm-width: calc(var(--oj-form-layout-column-max-width) / 2);
--oj-text-field-md-width: var(--oj-form-layout-column-max-width);
--oj-text-field-border-radius: var(--oj-core-border-radius-md);
--oj-text-field-border-width: 1px;
--oj-text-field-icon-to-text-padding: var(--oj-core-spacing-3x);
--oj-text-field-icon-to-edge-padding: var(--oj-core-spacing-3x);
--oj-text-field-text-to-edge-padding: var(--oj-core-spacing-3x);
--oj-text-field-text-align: start;
--oj-text-field-height: 2.75rem;
--oj-text-field-font-size: var(--oj-typography-body-md-font-size);
--oj-private-text-field-font-size-adjust: 100%;
--oj-text-field-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-text-field-border-color: rgba(var(--oj-palette-neutral-rgb-190), .5);
--oj-text-field-placeholder-color: var(--oj-core-text-color-secondary);
--oj-text-field-placeholder-font-style: normal;
--oj-text-field-text-color: var(--oj-core-text-color-primary);
--oj-text-field-bg-color-disabled: rgba(var(--oj-palette-neutral-rgb-0), .3);
--oj-text-field-border-color-disabled: var(--oj-core-color-disabled-2);
--oj-text-field-text-color-disabled: var(--oj-core-text-color-disabled);
--oj-text-field-border-color-focus: rgb(var(--oj-palette-brand-rgb-110));
--oj-text-field-inside-edge-height: 3.25rem;
--oj-text-field-inside-edge-padding-top: 0.8125rem;
--oj-text-field-inside-edge-label-color-error: rgb(var(--oj-palette-danger-rgb-100));
--oj-text-field-inside-edge-label-color-focus: rgb(var(--oj-palette-brand-rgb-110));
--oj-text-field-inside-edge-label-color-warning: rgb(var(--oj-palette-warning-rgb-100));
--oj-user-assistance-inline-text-color: var(--oj-core-text-color-secondary);
--oj-user-assistance-inline-font-size: var(--oj-typography-body-xs-font-size);
--oj-private-message-header-height: 1.5rem;
--oj-private-message-header-margin-end: 1rem;
--oj-private-message-category-text-color: var(--oj-core-text-color-primary);
--oj-private-message-summary-font-weight: var(--oj-typography-subheading-xl-font-weight);
--oj-private-message-summary-text-color: var(--oj-core-text-color-primary);
--oj-private-message-summary-font-size: var(--oj-typography-body-lg-font-size);
--oj-private-message-summary-line-height: var(--oj-typography-body-lg-line-height);
--oj-private-message-detail-text-color: var(--oj-core-text-color-primary);
--oj-private-message-detail-font-size: var(--oj-typography-body-sm-font-size);
--oj-private-message-detail-line-height: var(--oj-typography-body-sm-line-height);
--oj-private-message-time-text-color: var(--oj-core-text-color-secondary);
--oj-private-message-time-font-size: var(--oj-typography-body-xs-font-size);
--oj-private-message-time-margin-end: 0.25rem;
--oj-private-message-close-icon-display: block;
--oj-private-message-auto-timeout-close-icon-display: block;
--oj-private-message-general-inline-border-width: 0 0 1px 0;
--oj-private-message-general-inline-border-color: var(--oj-core-divider-color);
--oj-private-message-component-inline-font-size: var(--oj-typography-subheading-xs-font-size);
--oj-private-message-component-inline-font-weight: 600;
--oj-private-message-component-inline-bg-color-error: transparent;
--oj-private-message-component-inline-border-color-error: transparent;
--oj-private-message-component-inline-bg-color-warning: transparent;
--oj-private-message-component-inline-border-color-warning: transparent;
--oj-private-message-component-inline-bg-color-info: transparent;
--oj-private-message-component-inline-border-color-info: transparent;
--oj-private-message-component-inline-bg-color-confirmation: transparent;
--oj-private-message-component-inline-border-color-confirmation: transparent;
--oj-private-message-component-inline-margin-top: 0;
--oj-private-message-component-inline-border-width: 0;
--oj-private-message-component-inline-border-radius: 0;
--oj-private-message-component-inline-padding: 0;
--oj-private-message-radiocheckbox-inline-margin-top: 0;
--oj-private-message-radiocheckbox-inline-border-top-width: 0;
--oj-private-message-general-overlay-border-width: 1px;
--oj-private-message-general-overlay-border-color: var(--oj-core-divider-color);
--oj-private-message-general-overlay-border-radius: var(--oj-core-border-radius-lg);
--oj-private-message-general-overlay-box-shadow: var(--oj-core-box-shadow-md);
--oj-private-message-general-overlay-separator-margin: 0.5rem;
--oj-private-message-notification-overlay-font-size: var(--oj-typography-body-md-font-size);
--oj-private-message-notification-overlay-font-weight: 400;
--oj-private-message-notification-overlay-header-bg-color: var(--oj-core-info-secondary-3);
--oj-private-message-notification-overlay-body-bg-color: var(--oj-core-info-secondary-3);
--oj-private-message-notification-overlay-border-width: 0;
--oj-private-message-notification-overlay-border-color: initial;
--oj-private-message-notification-overlay-border-radius: var(--oj-core-border-radius-lg);
--oj-private-message-notification-overlay-box-shadow: var(--oj-core-box-shadow-md);
--oj-private-message-notification-overlay-separator-margin: 0.25rem;
--oj-private-message-notification-detail-text-color: var(--oj-core-text-color-primary);
--oj-private-message-component-icon-to-text-padding: 0.25rem;
--oj-private-messages-general-overlay-width: 60vw;
--oj-private-messages-general-overlay-max-width: 1536px;
--oj-private-messages-general-overlay-min-width: 614px;
--oj-private-messages-general-overlay-border-width: 0;
--oj-private-messages-general-overlay-border-color: initial;
--oj-private-messages-general-overlay-border-radius: var(--oj-core-border-radius-sm);
--oj-private-messages-general-overlay-box-shadow: none;
--oj-private-messages-notification-overlay-width: auto;
--oj-private-messages-notification-overlay-min-width: 307px;
--oj-private-messages-notification-overlay-max-width: 600px;
--oj-private-messages-notification-overlay-border-width: 0;
--oj-private-messages-notification-overlay-border-color: initial;
--oj-private-messages-notification-overlay-border-radius: initial;
--oj-private-messages-notification-overlay-box-shadow: initial;
--oj-radio-checkbox-width: auto;
--oj-radio-checkbox-row-height: 2.25rem;
--oj-radio-checkbox-input-size: 1rem;
--oj-radio-checkbox-input-color-unselected: rgb(var(--oj-palette-neutral-rgb-190));
--oj-radio-checkbox-input-color-selected: rgb(var(--oj-palette-neutral-rgb-190));
--oj-radio-checkbox-input-transform-active: scale(0.75);
--oj-radio-checkbox-label-to-edge-padding: 0;
--oj-radio-checkbox-input-to-label-padding: 0.5rem;
--oj-radio-checkbox-input-to-edge-padding: 0;
--oj-radio-checkbox-item-divider-color: transparent;
--oj-radio-checkbox-item-bg-color-active: transparent;
--oj-color-palette-swatch-inner-border-color: rgb(var(--oj-palette-neutral-rgb-170));
--oj-color-palette-swatch-outer-border-color-selected: rgb(var(--oj-palette-neutral-rgb-190));
--oj-color-palette-border-radius: 50%;
--oj-color-palette-swatch-margin: 1px;
--oj-color-palette-grid-font-size: var(--oj-typography-body-xs-font-size);
--oj-slider-thumb-width: 1.25rem;
--oj-slider-thumb-height: 1.25rem;
--oj-slider-thumb-border-radius: var(--oj-core-border-radius-lg);
--oj-slider-thumb-border-width: 0.125rem;
--oj-slider-thumb-bg-color: rgb(var(--oj-palette-neutral-rgb-10));
--oj-slider-thumb-border-color: rgb(var(--oj-palette-neutral-rgb-190));
--oj-slider-thumb-box-shadow: none;
--oj-slider-thumb-bg-color-hover: rgb(var(--oj-palette-neutral-rgb-30));
--oj-slider-thumb-box-shadow-hover: none;
--oj-slider-thumb-bg-color-active: rgb(var(--oj-palette-neutral-rgb-40));
--oj-slider-thumb-border-color-active: rgb(var(--oj-palette-neutral-rgb-190));
--oj-slider-thumb-box-shadow-active: none;
--oj-slider-thumb-scale-active: 1;
--oj-slider-thumb-bg-color-disabled: rgb(var(--oj-palette-neutral-rgb-0));
--oj-slider-thumb-border-color-disabled: var(--oj-core-text-color-disabled);
--oj-slider-track-thickness: 2px;
--oj-slider-track-bg-color: rgba(var(--oj-palette-neutral-rgb-170), 0.15);
--oj-slider-value-bg-color: rgb(var(--oj-palette-neutral-rgb-180));
--oj-slider-track-bg-color-disabled: var(--oj-core-color-disabled-2);
--oj-slider-value-bg-color-disabled: var(--oj-core-text-color-disabled);
--oj-color-spectrum-border-color: rgb(var(--oj-palette-neutral-rgb-50));
--oj-listbox-item-padding-horizontal: var(--oj-text-field-text-to-edge-padding);
--oj-conveyor-belt-box-shadow-width: 0.25rem;
--oj-private-conveyor-belt-global-arrow-visibility-default: auto;
--oj-data-grid-column-width: 6.25rem;
--oj-date-picker-cell-font-size: var(--oj-typography-body-sm-font-size);
--oj-date-picker-cell-border-color-today: rgb(var(--oj-palette-neutral-rgb-160));
--oj-drawer-reflow-bg-color: var(--oj-core-bg-color-content);
--oj-drawer-reflow-divider-color: rgb(var(--oj-palette-neutral-rgb-40));
--oj-drawer-overlay-bg-color: var(--oj-core-bg-color-content);
--oj-diagram-node-border-color-hover: rgb(var(--oj-palette-neutral-rgb-120));
--oj-diagram-link-color: rgb(var(--oj-palette-neutral-rgb-120));
--oj-diagram-node-bg-color: rgb(var(--oj-palette-neutral-rgb-170));
--oj-dialog-bg-color: rgb(var(--oj-palette-neutral-rgb-10));
--oj-dialog-border-color: rgba(var(--oj-palette-neutral-rgb-190), .1);
--oj-dialog-border-radius: var(--oj-core-border-radius-lg);
--oj-dialog-box-shadow: var(--oj-core-box-shadow-xl);
--oj-dialog-cancel-icon-margin-top: -1.5rem;
--oj-dialog-cancel-icon-margin-end: -1.5rem;
--oj-dialog-header-padding: 2.75rem 2rem 0.75rem;
--oj-dialog-body-padding: 0 2rem 1rem 2rem;
--oj-dialog-footer-padding: 1rem 2rem 2rem 2rem;
--oj-dialog-header-bg-color: transparent;
--oj-dialog-header-border-color: transparent;
--oj-dialog-title-font-size: var(--oj-typography-heading-xs-font-size);
--oj-dialog-title-line-height: var(--oj-typography-heading-xs-line-height);
--oj-dialog-title-font-weight: var(--oj-typography-heading-xs-font-weight);
--oj-dialog-title-text-color: var(--oj-core-text-color-primary);
--oj-file-picker-border-color: rgba(var(--oj-palette-neutral-rgb-190), 0.65);
--oj-file-picker-border-width: 1px;
--oj-file-picker-border-radius: var(--oj-core-border-radius-xl);
--oj-private-gantt-dependency-line-color: rgb(var(--oj-palette-neutral-rgb-100));
--oj-private-gantt-task-bg-color: rgb(95, 162, 186);
--oj-private-gantt-task-border-color: rgba(var(--oj-palette-neutral-rgb-190), 0.5);
--oj-private-gantt-task-progress-bg-color: var(--oj-private-gantt-task-bg-color);
--oj-private-gantt-task-progress-border-color: var(--oj-private-gantt-task-border-color);
--oj-private-gantt-task-drag-image-border-color: rgb(var(--oj-palette-neutral-rgb-180));
--oj-private-gantt-task-label-color: var(--oj-core-text-color-primary);
--oj-private-gantt-task-summary-bg-color: rgba(var(--oj-palette-neutral-rgb-160));
--oj-private-gantt-task-summary-border-color: var(--oj-private-gantt-task-border-color);
--oj-private-gantt-task-summary-progress-bg-color: var(--oj-private-gantt-task-summary-bg-color);
--oj-private-gantt-task-milestone-bg-color: rgba(var(--oj-palette-neutral-rgb-0));
--oj-private-gantt-task-milestone-border-color: var(--oj-private-gantt-task-border-color);
--oj-private-gantt-baseline-bg-color: rgb(var(--oj-palette-neutral-rgb-100));
--oj-private-gantt-baseline-milestone-bg-color: rgb(var(--oj-palette-neutral-rgb-100));
--oj-indexer-text-color: var(--oj-core-text-color-primary);
--oj-indexer-font-size: var(--oj-typography-body-sm-font-size);
--oj-private-input-number-button-global-chroming-default: borderless;
--oj-private-input-number-global-step-default: 0;
--oj-masonry-layout-tile-width: 10rem;
--oj-masonry-layout-tile-height: 10rem;
--oj-menu-icon-size: var(--oj-core-icon-size-lg);
--oj-menu-icon-to-edge-padding: 1rem;
--oj-menu-text-to-start-icon-padding: 1rem;
--oj-menu-text-to-end-icon-padding: 2rem;
--oj-menu-text-to-edge-padding: 1rem;
--oj-menu-divider-margin: var(--oj-core-spacing-2x) 0;
--oj-menu-item-text-color: var(--oj-core-text-color-primary);
--oj-menu-icon-color: var(--oj-core-text-color-primary);
--oj-menu-sheet-margin-horizontal: 0px;
--oj-private-menu-global-drop-down-open-animation: '{"effect":"zoomIn","transformOrigin":"#myPosition","duration":".25s"}';
--oj-private-menu-global-drop-down-close-animation: '{"effect":"none"}';
--oj-private-menu-global-sheet-open-animation: '{"effect":"slideIn","direction":"top","duration":".25s"}';
--oj-private-menu-global-sheet-close-animation: '{"effect":"slideOut","direction":"bottom","duration":".25s"}';
--oj-private-menu-global-submenu-open-animation: '{"effect":"zoomIn","transformOrigin":"#myPosition","duration":".25s"}';
--oj-private-menu-global-submenu-close-animation: '{"effect":"none"}';
--oj-private-menu-global-sheet-cancel-affordance: none;
--oj-private-menu-global-sheet-swipe-down-behavior: dismiss;
--oj-private-menu-global-drop-down-modality: modeless;
--oj-private-menu-global-sheet-modality: modal;
--oj-private-menu-global-sheet-margin-bottom: 0;
--oj-n-box-cell-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-n-box-cell-bg-color-maximized: rgb(var(--oj-palette-neutral-rgb-10));
--oj-n-box-node-bg-color: rgb(var(--oj-palette-neutral-rgb-30));
--oj-toolbar-button-margin: .5rem;
--oj-toolbar-borderless-button-margin: .125rem;
--oj-toolbar-separator-margin: .5rem;
--oj-private-toolbar-global-chroming-default: borderless;
--oj-tree-view-indent-width: 1.5rem;
--oj-tree-view-row-height: 2.5rem;
--oj-tree-view-text-color: var(--oj-core-text-color-primary);
--oj-private-tree-view-global-expand-animation: '{"effect":"expand"}';
--oj-private-tree-view-global-collapse-animation: '{"effect":"collapse"}';
--oj-private-tree-view-global-data-fadein-duration: .15s;
--oj-private-tree-view-global-selection-affordance-default: selector;
--oj-private-tree-view-global-load-indicator-default: skeleton;
--oj-navigation-list-font-size: var(--oj-typography-body-md-font-size);
--oj-navigation-list-font-weight: 600;
--oj-navigation-list-icon-margin: 0;
--oj-navigation-list-sliding-heading-font-size: var(--oj-typography-subheading-xs-font-size);
--oj-navigation-list-sliding-heading-font-weight: var(--oj-typography-subheading-xs-font-weight);
--oj-navigation-list-sliding-heading-line-height: var(--oj-typography-subheading-xs-line-height);
--oj-navigation-list-icon-to-text-padding: 0.5rem;
--oj-navigation-list-item-min-height: 3rem;
--oj-navigation-list-item-margin: 2rem;
--oj-private-navigation-list-item-border-width: 0.1875rem;
--oj-navigation-list-item-padding: 0;
--oj-navigation-list-item-label-color: var(--oj-core-text-color-secondary);
--oj-navigation-list-item-label-color-hover: var(--oj-core-text-color-primary);
--oj-navigation-list-item-bg-color-hover: transparent;
--oj-private-navigation-list-item-border-color-hover: transparent;
--oj-navigation-list-item-bg-color-selected: transparent;
--oj-navigation-list-item-border-color-selected: rgb(var(--oj-palette-brand-rgb-110));
--oj-navigation-list-item-label-color-selected: var(--oj-core-text-color-primary);
--oj-private-navigation-list-global-horizontal-add-animation-default: '[{"effect":"expand","direction":"width"},"fadeIn"]';
--oj-private-navigation-list-global-horizontal-remove-animation-default: '[{"effect":"collapse","direction":"width","persist":"all"},"fadeOut"]';
--oj-private-navigation-list-global-add-animation-default: '[{"effect":"expand"},"fadeIn"]';
--oj-private-navigation-list-global-remove-animation-default: '[{"effect":"collapse"},"fadeOut"]';
--oj-private-navigation-list-global-update-animation-default: '{"effect":"fadeIn"}';
--oj-private-navigation-list-global-expand-animation-default: '{"effect":"expand"}';
--oj-private-navigation-list-global-collapse-animation-default: '{"effect":"collapse"}';
--oj-private-navigation-list-global-slider-expand-animation-default: '{"effect":"slideIn","direction":"start","duration":"0.4s"}';
--oj-private-navigation-list-global-slider-collapse-animation-default: '{"effect":"slideIn","direction":"end","duration":"0.4s"}';
--oj-private-navigation-list-global-pointer-up-animation-default: '{"effect":"ripple"}';
--oj-private-navigation-list-global-hierarchy-menu-threshold-default: -1;
--oj-private-off-canvas-global-display-mode-default: overlay;
--oj-picto-chart-item-bg-color: rgba(var(--oj-palette-neutral-rgb-170), .15);
--oj-progress-bar-value-bg-color: rgb(var(--oj-palette-neutral-rgb-190));
--oj-progress-bar-height: 6px;
--oj-progress-bar-track-bg-color: rgba(var(--oj-palette-neutral-rgb-170), .15);
--oj-progress-bar-border-radius: 3px;
--oj-progress-circle-value-bg-color: rgb(var(--oj-palette-neutral-rgb-190));
--oj-progress-circle-sm-size: 1.5rem;
--oj-progress-circle-md-size: 3rem;
--oj-progress-circle-lg-size: 6rem;
--oj-progress-circle-sm-track-width: 2px;
--oj-progress-circle-md-track-width: 0.25rem;
--oj-progress-circle-lg-track-width: 0.375rem;
--oj-progress-circle-determinate-track-bg-color: rgba(var(--oj-palette-neutral-rgb-170), .15);
--oj-rating-gauge-border-color-selected: rgb(var(--oj-palette-neutral-rgb-190));
--oj-rating-gauge-color-selected: rgb(var(--oj-palette-neutral-rgb-190));
--oj-rating-gauge-border-color-unselected: rgb(var(--oj-palette-neutral-rgb-190));
--oj-rating-gauge-color-unselected: transparent;
--oj-rating-gauge-border-color-changed: var(--oj-rating-gauge-border-color-selected);
--oj-rating-gauge-color-changed: var(--oj-rating-gauge-color-selected);
--oj-rating-gauge-border-color-hover: var(--oj-rating-gauge-border-color-selected);
--oj-rating-gauge-color-hover: var(--oj-rating-gauge-color-selected);
--oj-rating-gauge-color-selected-disabled: var(--oj-core-text-color-disabled);
--oj-rating-gauge-color-unselected-disabled: var(--oj-core-color-disabled-2);
--oj-rating-gauge-sm-size: 16px;
--oj-rating-gauge-md-size: 20px;
--oj-rating-gauge-lg-size: 36px;
--oj-refresher-bg-color: rgb(var(--oj-palette-neutral-rgb-40));
--oj-switch-track-height: 1.25rem;
--oj-switch-track-width: 2.25rem;
--oj-switch-track-border-radius: var(--oj-core-border-radius-lg);
--oj-switch-thumb-to-track-horizontal-margin: 0.0625rem;
--oj-switch-track-bg-color: rgb(var(--oj-palette-neutral-rgb-90));
--oj-switch-track-border-color: transparent;
--oj-switch-thumb-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-switch-thumb-border-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-switch-thumb-height: calc(var(--oj-switch-track-height) - 0.25rem);
--oj-switch-thumb-width: var(--oj-switch-thumb-height);
--oj-switch-thumb-border-radius: var(--oj-core-border-radius-md);
--oj-switch-thumb-box-shadow: 0px 0.125rem 0.25rem 0px rgba(var(--oj-core-box-shadow-rgb), .1);
--oj-switch-track-bg-color-selected: rgb(var(--oj-palette-brand-rgb-100));
--oj-switch-track-border-color-selected: rgb(var(--oj-palette-brand-rgb-100));
--oj-switch-thumb-bg-color-selected: rgb(var(--oj-palette-neutral-rgb-0));
--oj-switch-thumb-border-color-selected: rgb(var(--oj-palette-neutral-rgb-0));
--oj-switch-thumb-box-shadow-selected: var(--oj-switch-thumb-box-shadow);
--oj-switch-track-bg-color-hover: rgba(var(--oj-palette-neutral-rgb-170), .6);
--oj-switch-track-border-color-hover: transparent;
--oj-switch-thumb-bg-color-hover: rgb(var(--oj-palette-neutral-rgb-10));
--oj-switch-thumb-border-color-hover: rgb(var(--oj-palette-neutral-rgb-10));
--oj-switch-thumb-box-shadow-hover: none;
--oj-switch-track-bg-color-selected-hover: rgb(var(--oj-palette-brand-rgb-110));
--oj-switch-track-border-color-selected-hover: rgb(var(--oj-palette-brand-rgb-110));
--oj-switch-thumb-bg-color-selected-hover: rgb(var(--oj-palette-neutral-rgb-10));
--oj-switch-thumb-border-color-selected-hover: rgb(var(--oj-palette-neutral-rgb-10));
--oj-switch-thumb-box-shadow-selected-hover: none;
--oj-switch-track-bg-color-active: rgba(var(--oj-palette-neutral-rgb-170), 0.68);
--oj-switch-track-border-color-active: transparent;
--oj-switch-thumb-bg-color-active: rgb(var(--oj-palette-neutral-rgb-10));
--oj-switch-thumb-border-color-active: rgb(var(--oj-palette-neutral-rgb-10));
--oj-switch-thumb-box-shadow-active: none;
--oj-switch-thumb-width-active: var(--oj-switch-thumb-width);
--oj-switch-track-bg-color-selected-active: rgb(var(--oj-palette-brand-rgb-120));
--oj-switch-track-border-color-selected-active: rgb(var(--oj-palette-brand-rgb-120));
--oj-switch-thumb-bg-color-selected-active: rgb(var(--oj-palette-neutral-rgb-10));
--oj-switch-thumb-border-color-selected-active: rgb(var(--oj-palette-neutral-rgb-10));
--oj-switch-thumb-box-shadow-selected-active: none;
--oj-switch-track-bg-color-disabled: var(--oj-core-color-disabled-1);
--oj-switch-track-border-color-disabled: transparent;
--oj-switch-thumb-bg-color-disabled: var(--oj-core-color-disabled-2);
--oj-switch-thumb-border-color-disabled: transparent;
--oj-switch-track-bg-color-selected-disabled: var(--oj-core-color-disabled-1);
--oj-switch-track-border-color-selected-disabled: transparent;
--oj-switch-thumb-bg-color-selected-disabled: var(--oj-core-color-disabled-2);
--oj-switch-thumb-border-color-selected-disabled: transparent;
--oj-private-tab-bar-border-radius-top-left: 0;
--oj-private-tab-bar-border-radius-top-right: 0;
--oj-private-tab-bar-border-radius-bottom-left: 0;
--oj-private-tab-bar-border-radius-bottom-right: 0;
--oj-tab-bar-icon-to-text-padding: 0.5rem;
--oj-tab-bar-icon-margin: 0;
--oj-tab-bar-item-margin: 0 2rem 0 0;
--oj-private-tab-bar-item-border-width-top: 0;
--oj-private-tab-bar-item-border-width-right: 0;
--oj-private-tab-bar-item-border-width-bottom: 0.1875rem;
--oj-private-tab-bar-item-border-width-left: 0;
--oj-tab-bar-item-min-height: 3rem;
--oj-tab-bar-item-line-height: var(--oj-typography-body-md-line-height);
--oj-tab-bar-item-font-size: var(--oj-typography-body-md-font-size);
--oj-tab-bar-item-padding: 0;
--oj-tab-bar-item-font-weight: 600;
--oj-private-tab-bar-item-border-color: transparent;
--oj-tab-bar-item-label-color: var(--oj-core-text-color-secondary);
--oj-tab-bar-item-label-color-hover: var(--oj-core-text-color-primary);
--oj-tab-bar-item-bg-color-hover: transparent;
--oj-private-tab-bar-item-border-color-hover: transparent;
--oj-tab-bar-item-bg-color-active: transparent;
--oj-tab-bar-item-border-color-active: rgb(var(--oj-palette-brand-rgb-70));
--oj-tab-bar-item-font-weight-selected: 600;
--oj-tab-bar-item-label-color-selected: var(--oj-core-text-color-primary);
--oj-tab-bar-item-bg-color-selected: transparent;
--oj-tab-bar-item-border-color-selected: rgb(var(--oj-palette-brand-rgb-110));
--oj-private-tab-bar-global-horizontal-add-animation-default: '[{"effect":"expand","direction":"width"},"fadeIn"]';
--oj-private-tab-bar-global-horizontal-remove-animation-default: '[{"effect":"collapse","direction":"width","persist":"all"},"fadeOut"]';
--oj-private-tab-bar-global-add-animation-default: '[{"effect":"expand"},"fadeIn"]';
--oj-private-tab-bar-global-remove-animation-default: '[{"effect":"collapse"},"fadeOut"]';
--oj-private-tab-bar-global-update-animation-default: '{"effect":"fadeIn"}';
--oj-private-tab-bar-global-pointerUp-animation-default: '{"effect":"ripple"}';
--oj-table-cell-padding-horizontal: 1rem;
--oj-private-table-global-display-default: list;
--oj-private-table-global-display-list-horizontal-grid-visible-default: disabled;
--oj-private-table-global-add-animation: '[{"effect":"slideIn","direction":"bottom"},"fadeIn"]';
--oj-private-table-global-remove-animation: '[{"effect":"slideOut","direction":"top"},"fadeOut"]';
--oj-private-table-global-update-animation: '{"effect":"fadeIn"}';
--oj-private-table-global-load-indicator-default: skeleton;
--oj-private-table-global-enable-selector-default: true;
--oj-private-table-global-sticky-default: true;
--oj-thematic-map-bg-color: rgb(var(--oj-palette-neutral-rgb-30));
--oj-thematic-map-border-color: rgb(var(--oj-palette-neutral-rgb-50));
--oj-thematic-map-marker-bg-color: rgb(var(--oj-palette-neutral-rgb-190));
--oj-thematic-map-marker-border-color: rgb(var(--oj-palette-neutral-rgb-190));
--oj-thematic-map-link-color: rgb(var(--oj-palette-neutral-rgb-190));
--oj-private-timeline-reference-object-color: rgb(var(--oj-palette-danger-rgb-100));
--oj-private-timeline-border-color: rgb(var(--oj-palette-neutral-rgb-50));
--oj-private-timeline-item-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-private-timeline-item-border-color: rgb(var(--oj-palette-neutral-rgb-160));
--oj-private-timeline-item-border-color-hover: var(--oj-private-timeline-item-border-color);
--oj-private-timeline-major-axis-separator-color: rgb(var(--oj-palette-neutral-rgb-100));
--oj-private-timeline-minor-axis-bg-color: var(--oj-private-timeline-series-bg-color);
--oj-private-timeline-minor-axis-border-color: rgb(var(--oj-palette-neutral-rgb-100));
--oj-private-timeline-minor-axis-separator-color: var(--oj-private-timeline-series-bg-color);
--oj-private-timeline-series-bg-color: rgb(var(--oj-palette-neutral-rgb-20));
--oj-private-timeline-item-padding: 16px;
--oj-private-timeline-item-border-radius: 6px;
--oj-private-timeline-item-duration-event-overflow-bg-color: rgb(var(--oj-palette-neutral-rgb-40));
--oj-train-step-width: 7rem;
--oj-train-padding: 0.75rem 0 0.75rem 0;
--oj-train-label-font-size: var(--oj-typography-body-sm-font-size);
--oj-train-label-font-weight: bold;
--oj-train-label-padding-top: 0.75rem;
--oj-train-button-diameter: 1.5rem;
--oj-train-button-font-size: var(--oj-typography-body-xs-font-size);
--oj-train-connector-height: 2px;
--oj-train-button-bg-color: rgb(var(--oj-palette-neutral-rgb-0));
--oj-train-button-border-color: rgba(var(--oj-palette-neutral-rgb-190), .6);
--oj-train-button-text-color: var(--oj-core-text-color-secondary);
--oj-train-label-color: var(--oj-core-text-color-secondary);
--oj-train-connector-color: rgba(var(--oj-palette-neutral-rgb-190), .3);
--oj-train-connector-padding: 0.625rem;
--oj-train-button-bg-color-visited: rgb(var(--oj-palette-success-rgb-110));
--oj-train-button-border-color-visited: transparent;
--oj-train-button-text-color-visited: var(--oj-core-neutral-contrast);
--oj-train-label-color-visited: var(--oj-core-text-color-primary);
--oj-train-button-bg-color-hover: rgb(var(--oj-palette-neutral-rgb-0));
--oj-train-button-border-color-hover: rgba(var(--oj-palette-brand-rgb-160), .6);
--oj-train-button-text-color-hover: rgba(var(--oj-palette-brand-rgb-160), .6);
--oj-train-step-bg-color-hover: var(--oj-core-bg-color-hover);
--oj-train-step-border-radius: var(--oj-core-border-radius-xl);
--oj-train-button-bg-color-active: rgb(var(--oj-palette-neutral-rgb-190));
--oj-train-button-border-color-active: transparent;
--oj-train-button-text-color-active: rgb(var(--oj-palette-neutral-rgb-0));
--oj-train-label-font-weight-active: bold;
--oj-train-step-bg-color-active: var(--oj-core-bg-color-active);
--oj-train-button-bg-color-selected: rgb(var(--oj-palette-neutral-rgb-190));
--oj-train-button-border-color-selected: transparent;
--oj-train-button-text-color-selected: rgb(var(--oj-palette-neutral-rgb-0));
--oj-train-label-font-weight-selected: bold;
--oj-train-label-color-selected: var(--oj-core-text-color-primary);
--oj-train-connector-color-selected: rgba(var(--oj-palette-neutral-rgb-190), .3);
--oj-train-connector-padding-selected: 0px;
--oj-train-button-bg-color-disabled: rgb(var(--oj-palette-neutral-rgb-0));
--oj-train-button-border-color-disabled: var(--oj-core-text-color-disabled);
--oj-train-button-text-color-disabled: var(--oj-core-text-color-disabled);
--oj-train-button-opacity-disabled: 1;
--oj-train-label-color-disabled: var(--oj-core-text-color-disabled);
--oj-treemap-node-header-bg-color: rgb(var(--oj-palette-neutral-rgb-20));
--oj-treemap-node-header-bg-color-selected: rgb(var(--oj-palette-brand-rgb-40));
--oj-treemap-node-header-border-color: rgb(var(--oj-palette-neutral-rgb-40));
--oj-treemap-node-header-border-color-hover: rgb(var(--oj-palette-neutral-rgb-40));
--oracle-sans: 'Oracle Sans',
-apple-system,
BlinkMacSystemFont,
'Segoe UI',
'Helvetica Neue',
Helvetica,
Arial,
sans-serif;
--georgia-font-family: 'Georgia',
'Times New Roman',
'Times',
'Garamond',
'Noto Sans Arabic',
'Noto Sans Arabic UI',
'Noto Sans Hebrew',
sans-serif;
--code-block-font-family: 'Courier New',
Courier,
monospace;
--alert-warning-background: #F0CC71;
--alert-promo-background: #D7E5E5;
--border-color: 22,
21,
19;
--anchor-active-color: #70665E;
--cloud-free-tier-disclaimer-bg-color: #F4EFE1;
--oracle-ico-color: #C74634;