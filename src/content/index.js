// Create popup container with Shadow DOM for better isolation
const popupContainer = document.createElement('div');
popupContainer.id = 'gemini-translator-container';
popupContainer.style.cssText = `
  position: absolute !important;
  z-index: 2147483647 !important;
  pointer-events: none !important;
  display: none !important;
`;

// Create shadow root for complete style isolation
const shadowRoot = popupContainer.attachShadow({ mode: 'closed' });

// Create the actual popup inside shadow DOM
const popup = document.createElement('div');
popup.className = 'gemini-translator-popup';
popup.style.cssText = `
  pointer-events: auto !important;
  position: relative !important;
  max-width: 600px !important;
  min-width: 280px !important;
  width: auto !important;
  background: transparent !important;
  border: none !important;
  border-radius: 0.5rem !important;
  padding: 0 !important;
  overflow: hidden !important;
`;

shadowRoot.appendChild(popup);
document.body.appendChild(popupContainer);

function initializeContentScript() {
  document.addEventListener('mouseup', handleSelectionEvent);
  document.addEventListener('keyup', handleSelectionEvent);

  chrome.runtime.onMessage.addListener((message) => {
    handleBackgroundMessages(message);
  });
}

function handleSelectionEvent(event) {
  const selection = window.getSelection();
  const selectedText = selection.toString().trim();

  if (!selectedText) {
    hidePopup();
    return;
  }

  let position;
  try {
    if (event && event.type === 'mouseup' && event.clientX != null && event.clientY != null) {
      position = {
        x: event.clientX + window.scrollX + 10,
        y: event.clientY + window.scrollY
      };
    } else if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      position = {
        x: rect.left + window.scrollX + (rect.width / 2),
        y: rect.bottom + window.scrollY
      };
    } else {
      hidePopup();
      return;
    }
  } catch (error) {
    console.error("Error getting selection position:", error);
    hidePopup();
    return;
  }


  showTranslationButton(position, selectedText);
}

function handleBackgroundMessages(message) {

  switch (message.action) {
    case 'showTranslation':
      showTranslationPopup(message.translation, message.position);
      break;
    case 'showError':
      showErrorPopup(message.error, message.position);
      break;
    case 'translateText':
      const selection = window.getSelection();
      if (selection.toString().trim() === message.text.trim()) {
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        const position = {
          x: rect.left + window.scrollX + (rect.width / 2),
          y: rect.bottom + window.scrollY
        };
        sendMessageToBackground({
          action: 'translate',
          text: message.text,

          position
        });
      }
      break;
    case 'updateFontStyle':
      if (popup.style.display === 'block') {
        updatePopupStyles();
      }
      break;
    case 'updateFontSize':
      if (popup.style.display === 'block') {
        updatePopupStyles();
      }
      break;
    default:

      break;
  }
}

function sendMessageToBackground(message) {
  chrome.runtime.sendMessage(message);
}

function showTranslationButton(position, text) {
  const loading = window.loading || {
    show: () => { },
    hide: () => { }
  };
  let button = document.getElementById('gemini-translator-button');

  if (!button) {
    button = document.createElement('button');
    button.id = 'gemini-translator-button';
    button.className = 'gemini-translator-button';
    document.body.appendChild(button);

    button.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M5 8l6 6"></path>
        <path d="M4 14l6-6 2-3"></path>
        <path d="M2 5h12"></path>
        <path d="M7 2h1"></path>
        <path d="M22 22l-5-10-5 10"></path>
        <path d="M14 18h6"></path>
      </svg>
    `;
  }

  button.style.left = `${position.x}px`;
  button.style.top = `${position.y}px`;
  button.style.display = 'flex';

  button.onclick = function () {
    if (window.showLoading) {
      window.showLoading(position);
    }

    sendMessageToBackground({
      action: 'translate',
      text: text,
      position: position
    });
    button.classList.add('loading');
  };
}

function formatAndSanitizeMarkdown(text) {
  if (!text || typeof text !== 'string') {
    return '<p>Translation not available.</p>';
  }

  try {
    marked.setOptions({
      sanitize: true,
      silent: true,
      headerIds: false,
      mangle: false
    });

    const rawHtml = marked.parse(text);
    const sanitizedHtml = DOMPurify.sanitize(rawHtml, {
      ALLOWED_TAGS: [
        // Basic formatting
        'p', 'br', 'strong', 'em', 'i', 'b', 'code', 'pre',
        // Lists
        'ul', 'ol', 'li',
        // Headings (limited to h3 and below for UI consistency)
        'h3', 'h4', 'h5', 'h6',
        // Other elements
        'blockquote', 'span'
      ],
      ALLOWED_ATTR: [],
      ADD_ATTR: ['class'],
      FORBID_TAGS: ['style', 'script', 'iframe', 'frame', 'object', 'embed', 'form', 'input', 'textarea', 'select', 'button'],
      FORBID_ATTR: ['style', 'onerror', 'onload', 'onclick', 'onmouseover'],
      ALLOW_DATA_ATTR: false,
      USE_PROFILES: { html: true },
      SANITIZE_DOM: true,
      KEEP_CONTENT: true,
      RETURN_DOM: false,
      RETURN_DOM_FRAGMENT: false,
      RETURN_DOM_IMPORT: false,
      WHOLE_DOCUMENT: false,
      FORCE_BODY: true
    });

    return sanitizedHtml || '<p>Translation not available.</p>';
  } catch (error) {
    console.error('Error formatting markdown:', error);
    return '<p>Error formatting translation.</p>';
  }
}

function generatePopupStyles(fontFamily, fontSize) {
  const size = parseInt(fontSize, 10) || 15;
  const lineHeight = size > 20 ? 1.8 : 1.6;
  const letterSpacing = fontFamily && fontFamily.includes('Pacifico') ? '0.5px' : 'normal';

  return `
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Pacifico&display=swap');
    @import url('https://fonts.googleapis.com/css2?family=Baloo+2:wght@400..800&family=Be+Vietnam+Pro:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Cabin:ital,wght@0,400..700;1,400..700&family=Comfortaa:wght@300..700&family=Crimson+Pro:ital,wght@0,200..900;1,200..900&family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&family=Fira+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Fraunces:ital,opsz,wght@0,9..144,100..900;1,9..144,100..900&family=Geologica:wght@100..900&family=Lexend+Deca:wght@100..900&family=Literata:ital,opsz,wght@0,7..72,200..900;1,7..72,200..900&family=Mali:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;1,200;1,300;1,400;1,500;1,600;1,700&family=Manrope:wght@200..800&family=Maven+Pro:wght@400..900&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Mulish:ital,wght@0,200..1000;1,200..1000&family=Newsreader:ital,opsz,wght@0,6..72,200..800;1,6..72,200..800&family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Nunito:ital,wght@0,200..1000;1,200..1000&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Petrona:ital,wght@0,100..900;1,100..900&family=Questrial&family=Quicksand:wght@300..700&family=Raleway:ital,wght@0,100..900;1,100..900&family=Roboto+Mono:ital,wght@0,100..700;1,100..700&family=Roboto:ital,wght@0,100..900;1,100..900&family=Space+Grotesk:wght@300..700&family=Varela+Round&family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap');

    /* Base Reset for Shadow DOM */
    * {
      font-family: ${fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif'} !important;
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
      box-sizing: border-box !important;
    }

    /* Popup Container */
    .gemini-translator-popup {
      position: relative !important;
      max-width: 600px !important;
      min-width: 280px !important;
      width: auto !important;
      background: transparent !important;
      border: none !important;
      border-radius: 0.5rem !important;
      padding: 0 !important;
      overflow: hidden !important;
      animation: gemini-translator-fade-in 0.3s ease-out forwards !important;
    }

    /* Content Container */
    .gemini-translator-content {
      position: relative !important;
      display: flex !important;
      flex-direction: column !important;
      gap: 8px !important;
      color: #1a1a1a !important;
      font-family: ${fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif'} !important;
      font-size: ${size}px !important;
    }

    /* Card Container */
    .gemini-translator-card {
      position: relative !important;
      display: flex !important;
      flex-direction: column !important;
      border-radius: 0.5rem !important;
      background-color: #18385a !important;
      padding: 6px !important;
      overflow: hidden !important;
      box-shadow: inset 0 0 30px rgba(255, 255, 255, 0.08), 0 4px 15px rgba(0, 0, 0, 0.2) !important;
      border: 1px solid rgba(255, 255, 255, 0.05) !important;
    }

    .gemini-translator-card::before {
      content: '' !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      height: 70% !important;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.05), transparent) !important;
      pointer-events: none !important;
      border-radius: 0.5rem 0.5rem 2rem 2rem !important;
      backdrop-filter: blur(1px) !important;
      opacity: 0.8 !important;
    }

    /* Translation Content */
    .gemini-translator-translation {
      color: #94a3b8 !important;
      line-height: ${lineHeight} !important;
      font-size: ${size}px !important;
      letter-spacing: ${letterSpacing} !important;
      word-wrap: break-word !important;
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
      text-rendering: optimizeLegibility !important;
      position: relative !important;
      overflow: hidden !important;
      padding: 4px !important;
      margin: 2px !important;
      background: rgba(33, 40, 48, 0.1) !important;
      backdrop-filter: blur(8px) !important;
      border-radius: 6px !important;
      font-family: ${fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif'} !important;
    }

    /* Typography */
    .gemini-translator-translation p,
    .gemini-translator-translation h1,
    .gemini-translator-translation h2,
    .gemini-translator-translation h3,
    .gemini-translator-translation h4,
    .gemini-translator-translation h5,
    .gemini-translator-translation h6,
    .gemini-translator-translation li,
    .gemini-translator-translation ul,
    .gemini-translator-translation ol {
      font-family: ${fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif'} !important;
      font-size: ${size}px !important;
      line-height: ${lineHeight} !important;
      letter-spacing: ${letterSpacing} !important;
      color: #94a3b8 !important;
      margin: 4px 0 !important;
    }

    .gemini-translator-translation h1,
    .gemini-translator-translation h2,
    .gemini-translator-translation h3,
    .gemini-translator-translation h4,
    .gemini-translator-translation h5,
    .gemini-translator-translation h6 {
      font-weight: 600 !important;
      margin: 8px 0 !important;
    }

    .gemini-translator-translation ul,
    .gemini-translator-translation ol {
      padding-left: 16px !important;
    }

    .gemini-translator-translation strong,
    .gemini-translator-translation b {
      font-weight: 600 !important;
      color: rgba(255, 255, 255, 0.98) !important;
    }

    .gemini-translator-translation em,
    .gemini-translator-translation i {
      font-style: italic !important;
      color: rgba(255, 255, 255, 0.9) !important;
    }

    .gemini-translator-translation code {
      background-color: rgba(255, 255, 255, 0.05) !important;
      padding: 1px 3px !important;
      border-radius: 3px !important;
      color: #94a3b8 !important;
      font-family: 'Roboto Mono', monospace !important;
    }

    .gemini-translator-translation pre {
      white-space: pre-wrap !important;
      word-wrap: break-word !important;
      background-color: rgba(255, 255, 255, 0.05) !important;
      padding: 8px !important;
      border-radius: 6px !important;
      color: #94a3b8 !important;
      font-family: 'Roboto Mono', monospace !important;
      margin: 8px 0 !important;
    }

    /* Error styles */
    .gemini-translator-error {
      background-color: rgba(224, 92, 110, 0.1) !important;
      border: 1px solid rgba(224, 92, 110, 0.3) !important;
      border-radius: 8px !important;
      padding: 16px !important;
    }

    .gemini-translator-header {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      margin-bottom: 12px !important;
      font-weight: 600 !important;
      color: #e05c6e !important;
    }

    .gemini-translator-close {
      width: 24px !important;
      height: 24px !important;
      border-radius: 4px !important;
      border: none !important;
      background: transparent !important;
      color: rgba(255, 255, 255, 0.7) !important;
      font-size: 18px !important;
      cursor: pointer !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      transition: all 0.2s !important;
    }

    .gemini-translator-close:hover {
      background: rgba(255, 255, 255, 0.1) !important;
      color: rgba(255, 255, 255, 0.9) !important;
    }

    .gemini-translator-message {
      color: #e05c6e !important;
      font-size: 14px !important;
      line-height: 1.5 !important;
      font-weight: 500 !important;
    }

    /* Animations */
    @keyframes gemini-translator-fade-in {
      from {
        opacity: 0 !important;
        transform: translateY(-10px) !important;
        filter: blur(5px) !important;
      }
      to {
        opacity: 1 !important;
        transform: translateY(0) !important;
        filter: blur(0) !important;
      }
    }
  `;
}

function showTranslationPopup(translation, position, forcePosition = null) {
  const button = document.getElementById('gemini-translator-button');
  if (button) {
    button.classList.remove('loading');
    button.style.display = 'none';
  }

  const formattedTranslation = formatAndSanitizeMarkdown(translation);
  chrome.storage.local.get(['fontFamily', 'fontSize'], function (fontResult) {
    const fontFamily = fontResult.fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif';
    const fontSize = fontResult.fontSize || '15';

    const styles = generatePopupStyles(fontFamily, fontSize);

    // Create style element for shadow DOM
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;

    // Clear previous content and add new content
    popup.innerHTML = '';
    popup.appendChild(styleElement);

    const contentDiv = document.createElement('div');
    contentDiv.className = 'gemini-translator-content';
    contentDiv.innerHTML = `
      <div class="gemini-translator-card">
        <div class="gemini-translator-translation">
          ${formattedTranslation}
        </div>
      </div>
    `;

    popup.appendChild(contentDiv);

    positionPopup(position);
    popupContainer.style.display = 'block';

    document.addEventListener('mousedown', closeOnClickOutside);
  });
}

function showErrorPopup(error, position) {
  chrome.storage.local.get(['fontFamily', 'fontSize'], function (fontResult) {
    const fontFamily = fontResult.fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif';
    const fontSize = fontResult.fontSize || '15';

    const styles = generatePopupStyles(fontFamily, fontSize);

    // Create style element for shadow DOM
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;

    // Clear previous content and add new content
    popup.innerHTML = '';
    popup.appendChild(styleElement);

    const contentDiv = document.createElement('div');
    contentDiv.className = 'gemini-translator-content gemini-translator-error';
    contentDiv.innerHTML = `
      <div class="gemini-translator-header">
        <span>Translation Error</span>
        <button class="gemini-translator-close">&times;</button>
      </div>
      <div class="gemini-translator-message">${error}</div>
    `;

    popup.appendChild(contentDiv);

    const closeBtn = popup.querySelector('.gemini-translator-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', hidePopup);
    }

    positionPopup(position);
    popupContainer.style.display = 'block';

    document.addEventListener('mousedown', closeOnClickOutside);
  });
}

function updatePopupStyles() {
  chrome.storage.local.get(['fontFamily', 'fontSize'], function (fontResult) {
    const fontFamily = fontResult.fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif';
    const fontSize = fontResult.fontSize || '15';

    const styles = generatePopupStyles(fontFamily, fontSize);

    const styleElement = popup.querySelector('style');
    if (styleElement) {
      styleElement.textContent = styles;
    } else {
      const newStyle = document.createElement('style');
      newStyle.textContent = styles;
      popup.prepend(newStyle);
    }
  });
}

function hidePopup() {
  popupContainer.style.display = 'none';
  document.removeEventListener('mousedown', closeOnClickOutside);

  const button = document.getElementById('gemini-translator-button');
  if (button) {
    button.style.display = 'none';
  }
}

function positionPopup(position) {
  chrome.storage.local.get(['autoPosition', 'defaultPosition'], function (prefs) {
    const autoPositionEnabled = typeof prefs.autoPosition === 'boolean' ? prefs.autoPosition : true;
    const defaultPositionValue = typeof prefs.defaultPosition === 'string' ? prefs.defaultPosition : 'below';

    // Set initial position to make popup visible so we can measure it
    popupContainer.style.left = '0px';
    popupContainer.style.top = '0px';

    // Use a setTimeout to ensure the popup has been rendered and has dimensions
    setTimeout(() => {
      // Get actual dimensions after rendering
      const popupWidth = popup.offsetWidth || 300;
      const popupHeight = popup.offsetHeight || 150;

      let left = position.x - (popupWidth / 2);
      let top;

      // Set initial position based on settings
      if (autoPositionEnabled) {
        // Default position below text
        top = position.y + 10;

        // Auto-adjust if it would go off-screen
        if (top + popupHeight > window.scrollY + window.innerHeight - 10) {
          // Not enough room below, try above
          top = position.y - popupHeight - 10;

          // If still not enough room, position at the top of viewport with scroll
          if (top < window.scrollY + 10) {
            top = window.scrollY + 10;
          }
        }
      } else {
        // Use the specified default position
        switch (defaultPositionValue) {
          case 'above':
            top = position.y - popupHeight - 10;
            break;
          case 'cursor':
            // Position top edge at cursor Y
            top = position.y;
            break;
          case 'below':
          default:
            // Position below text (default)
            top = position.y + 10;
            break;
        }

        // Safety checks to ensure popup stays within viewport
        if (top + popupHeight > window.scrollY + window.innerHeight - 10) {
          top = window.scrollY + window.innerHeight - popupHeight - 10;
        }
        if (top < window.scrollY + 10) {
          top = window.scrollY + 10;
        }
      }

      // Ensure popup stays within horizontal bounds
      if (left < 10) {
        left = 10;
      }
      if (left + popupWidth > window.innerWidth - 10) {
        left = window.innerWidth - popupWidth - 10;
      }

      // Apply the calculated position to the container
      popupContainer.style.left = `${left}px`;
      popupContainer.style.top = `${top}px`;
    }, 10); // Slightly longer timeout to ensure rendering
  });
}

function closeOnClickOutside(event) {
  if (!popupContainer.contains(event.target) && event.target.id !== 'gemini-translator-button') {
    hidePopup();
  }
}

initializeContentScript();
