<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shadow DOM Popup Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        
        .oracle-style {
            /* Simulate Oracle docs aggressive CSS */
            font-family: "Oracle Sans", Arial, sans-serif !important;
            color: #312d2a !important;
            background: #f8f8f8 !important;
        }
        
        .oracle-style * {
            font-family: "Oracle Sans", <PERSON><PERSON>, sans-serif !important;
            color: #312d2a !important;
        }
        
        .oracle-style .gemini-translator-popup {
            display: none !important; /* This would break normal popup */
        }
        
        .oracle-style .gemini-translator-popup * {
            background: red !important; /* This would break styling */
            color: black !important;
        }
        
        .selectable-text {
            user-select: text;
            cursor: text;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        .test-button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="test-content">
        <h1>Shadow DOM Popup Test</h1>
        <p>This page tests the new Shadow DOM implementation for better compatibility with complex websites like Oracle docs.</p>
        
        <div class="test-section">
            <h2>Normal Environment</h2>
            <div class="selectable-text">
                Select this text to test the popup in a normal environment. The popup should work perfectly here.
            </div>
            <button class="test-button" onclick="testPopup('Hello World! This is a test translation.', 'normal')">Test Normal Popup</button>
        </div>
        
        <div class="test-section oracle-style">
            <h2>Oracle-style Aggressive CSS Environment</h2>
            <div class="selectable-text">
                Select this text to test the popup in an environment with aggressive CSS that tries to override extension styles. The Shadow DOM should protect our styles.
            </div>
            <button class="test-button" onclick="testPopup('Xin chào! Đây là bản dịch thử nghiệm trong môi trường CSS khắc nghiệt.', 'oracle')">Test Oracle-style Popup</button>
        </div>
        
        <div class="test-section">
            <h2>Error Test</h2>
            <button class="test-button" onclick="testErrorPopup()">Test Error Popup</button>
        </div>
    </div>

    <script>
        // Simulate the extension's popup functionality
        function testPopup(translation, environment) {
            const position = {
                x: window.innerWidth / 2,
                y: window.innerHeight / 2
            };
            
            // Simulate showing translation popup
            showTestTranslationPopup(translation, position);
        }
        
        function testErrorPopup() {
            const position = {
                x: window.innerWidth / 2,
                y: window.innerHeight / 2 + 100
            };
            
            showTestErrorPopup('Test error: Connection failed', position);
        }
        
        // Simplified version of the Shadow DOM popup implementation
        function createShadowPopup() {
            const popupContainer = document.createElement('div');
            popupContainer.id = 'test-gemini-translator-container';
            popupContainer.style.cssText = `
                position: absolute !important;
                z-index: 2147483647 !important;
                pointer-events: none !important;
                display: none !important;
            `;

            const shadowRoot = popupContainer.attachShadow({ mode: 'closed' });
            const popup = document.createElement('div');
            popup.className = 'gemini-translator-popup';
            popup.style.cssText = `
                pointer-events: auto !important;
                position: relative !important;
                max-width: 600px !important;
                min-width: 280px !important;
                width: auto !important;
                background: transparent !important;
                border: none !important;
                border-radius: 0.5rem !important;
                padding: 0 !important;
                overflow: hidden !important;
            `;

            shadowRoot.appendChild(popup);
            document.body.appendChild(popupContainer);
            
            return { popupContainer, popup };
        }
        
        function getTestStyles() {
            return `
                @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap');
                
                * {
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, system-ui, sans-serif !important;
                    -webkit-font-smoothing: antialiased !important;
                    -moz-osx-font-smoothing: grayscale !important;
                    box-sizing: border-box !important;
                }
                
                .gemini-translator-popup {
                    position: relative !important;
                    max-width: 600px !important;
                    min-width: 280px !important;
                    width: auto !important;
                    background: transparent !important;
                    border: none !important;
                    border-radius: 0.5rem !important;
                    padding: 0 !important;
                    overflow: hidden !important;
                    animation: gemini-translator-fade-in 0.3s ease-out forwards !important;
                }
                
                .gemini-translator-content {
                    position: relative !important;
                    display: flex !important;
                    flex-direction: column !important;
                    gap: 8px !important;
                    color: #1a1a1a !important;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, system-ui, sans-serif !important;
                    font-size: 15px !important;
                }
                
                .gemini-translator-card {
                    position: relative !important;
                    display: flex !important;
                    flex-direction: column !important;
                    border-radius: 0.5rem !important;
                    background-color: #18385a !important;
                    padding: 6px !important;
                    overflow: hidden !important;
                    box-shadow: inset 0 0 30px rgba(255, 255, 255, 0.08), 0 4px 15px rgba(0, 0, 0, 0.2) !important;
                    border: 1px solid rgba(255, 255, 255, 0.05) !important;
                }
                
                .gemini-translator-card::before {
                    content: '' !important;
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    height: 70% !important;
                    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.05), transparent) !important;
                    pointer-events: none !important;
                    border-radius: 0.5rem 0.5rem 2rem 2rem !important;
                    backdrop-filter: blur(1px) !important;
                    opacity: 0.8 !important;
                }
                
                .gemini-translator-translation {
                    color: #94a3b8 !important;
                    line-height: 1.6 !important;
                    font-size: 15px !important;
                    word-wrap: break-word !important;
                    -webkit-font-smoothing: antialiased !important;
                    -moz-osx-font-smoothing: grayscale !important;
                    text-rendering: optimizeLegibility !important;
                    position: relative !important;
                    overflow: hidden !important;
                    padding: 4px !important;
                    margin: 2px !important;
                    background: rgba(33, 40, 48, 0.1) !important;
                    backdrop-filter: blur(8px) !important;
                    border-radius: 6px !important;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, system-ui, sans-serif !important;
                }
                
                .gemini-translator-error {
                    background-color: rgba(224, 92, 110, 0.1) !important;
                    border: 1px solid rgba(224, 92, 110, 0.3) !important;
                    border-radius: 8px !important;
                    padding: 16px !important;
                }
                
                .gemini-translator-header {
                    display: flex !important;
                    justify-content: space-between !important;
                    align-items: center !important;
                    margin-bottom: 12px !important;
                    font-weight: 600 !important;
                    color: #e05c6e !important;
                }
                
                .gemini-translator-close {
                    width: 24px !important;
                    height: 24px !important;
                    border-radius: 4px !important;
                    border: none !important;
                    background: transparent !important;
                    color: rgba(255, 255, 255, 0.7) !important;
                    font-size: 18px !important;
                    cursor: pointer !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    transition: all 0.2s !important;
                }
                
                .gemini-translator-close:hover {
                    background: rgba(255, 255, 255, 0.1) !important;
                    color: rgba(255, 255, 255, 0.9) !important;
                }
                
                .gemini-translator-message {
                    color: #e05c6e !important;
                    font-size: 14px !important;
                    line-height: 1.5 !important;
                    font-weight: 500 !important;
                }
                
                @keyframes gemini-translator-fade-in {
                    from {
                        opacity: 0 !important;
                        transform: translateY(-10px) !important;
                        filter: blur(5px) !important;
                    }
                    to {
                        opacity: 1 !important;
                        transform: translateY(0) !important;
                        filter: blur(0) !important;
                    }
                }
            `;
        }
        
        function showTestTranslationPopup(translation, position) {
            // Remove existing popup
            const existing = document.getElementById('test-gemini-translator-container');
            if (existing) {
                existing.remove();
            }
            
            const { popupContainer, popup } = createShadowPopup();
            
            const styles = getTestStyles();
            const styleElement = document.createElement('style');
            styleElement.textContent = styles;
            
            popup.innerHTML = '';
            popup.appendChild(styleElement);
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'gemini-translator-content';
            contentDiv.innerHTML = `
                <div class="gemini-translator-card">
                    <div class="gemini-translator-translation">
                        ${translation}
                    </div>
                </div>
            `;
            
            popup.appendChild(contentDiv);
            
            popupContainer.style.left = `${position.x - 150}px`;
            popupContainer.style.top = `${position.y}px`;
            popupContainer.style.display = 'block';
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                popupContainer.remove();
            }, 5000);
        }
        
        function showTestErrorPopup(error, position) {
            // Remove existing popup
            const existing = document.getElementById('test-gemini-translator-container');
            if (existing) {
                existing.remove();
            }
            
            const { popupContainer, popup } = createShadowPopup();
            
            const styles = getTestStyles();
            const styleElement = document.createElement('style');
            styleElement.textContent = styles;
            
            popup.innerHTML = '';
            popup.appendChild(styleElement);
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'gemini-translator-content gemini-translator-error';
            contentDiv.innerHTML = `
                <div class="gemini-translator-header">
                    <span>Translation Error</span>
                    <button class="gemini-translator-close">&times;</button>
                </div>
                <div class="gemini-translator-message">${error}</div>
            `;
            
            popup.appendChild(contentDiv);
            
            const closeBtn = popup.querySelector('.gemini-translator-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    popupContainer.remove();
                });
            }
            
            popupContainer.style.left = `${position.x - 150}px`;
            popupContainer.style.top = `${position.y}px`;
            popupContainer.style.display = 'block';
            
            // Auto hide after 8 seconds
            setTimeout(() => {
                if (document.body.contains(popupContainer)) {
                    popupContainer.remove();
                }
            }, 8000);
        }
    </script>
</body>
</html>
